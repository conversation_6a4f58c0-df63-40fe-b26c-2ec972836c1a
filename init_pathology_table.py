#!/usr/bin/env python3
"""
初始化病理报告表的脚本
"""

from app import create_app, db
from app.models import PathologyReport
from datetime import datetime

def init_pathology_table():
    """初始化病理报告表"""
    app = create_app()
    
    with app.app_context():
        try:
            # 创建所有表（包括新的pathology_report表）
            db.create_all()
            print("数据库表创建成功!")
            
            # 可选：插入一些测试数据
            print("正在插入测试数据...")
            
            # 示例测试数据
            test_reports = [
                PathologyReport(
                    user_id=1,  # 假设存在user_id为1的用户
                    report_date=datetime(2024, 1, 15),
                    hospital="北京协和医院",
                    report_type="活检病理",
                    specimen_type="胃窦粘膜",
                    clinical_diagnosis="胃炎",
                    pathology_diagnosis="慢性浅表性胃炎",
                    microscopic_description="胃窦粘膜慢性炎症，腺体轻度萎缩",
                    macroscopic_description="灰白色组织一块，大小0.3×0.2×0.1cm",
                    doctor_name="张医生",
                    comment="建议定期复查",
                    status="normal"
                ),
                PathologyReport(
                    user_id=1,
                    report_date=datetime(2024, 2, 20),
                    hospital="北京协和医院",
                    report_type="细胞学检查",
                    specimen_type="宫颈刮片",
                    clinical_diagnosis="宫颈糜烂",
                    pathology_diagnosis="宫颈鳞状上皮内病变低级别（LSIL）",
                    microscopic_description="可见少量异型鳞状细胞",
                    macroscopic_description="宫颈刮片标本",
                    doctor_name="李医生",
                    comment="建议进一步检查",
                    status="abnormal"
                ),
                PathologyReport(
                    user_id=1,
                    report_date=datetime(2024, 3, 10),
                    hospital="北京大学第一医院",
                    report_type="免疫组化",
                    specimen_type="淋巴结",
                    clinical_diagnosis="淋巴结肿大",
                    pathology_diagnosis="反应性淋巴结增生",
                    microscopic_description="淋巴滤泡增生活跃，未见恶性细胞",
                    macroscopic_description="淋巴结组织一块，大小1.5×1.0×0.8cm",
                    doctor_name="王医生",
                    comment="良性病变，无需特殊处理",
                    status="normal"
                )
            ]
            
            # 添加测试数据到数据库
            for report in test_reports:
                db.session.add(report)
            
            db.session.commit()
            print(f"成功插入 {len(test_reports)} 条测试数据!")
            
            # 验证数据
            count = PathologyReport.query.count()
            print(f"当前病理报告表中共有 {count} 条记录")
            
        except Exception as e:
            print(f"初始化失败: {e}")
            db.session.rollback()

if __name__ == "__main__":
    init_pathology_table()
