#!/usr/bin/env python3
"""
测试病理报告查询接口的脚本
"""

import requests
import json
from datetime import datetime

# 配置
BASE_URL = "http://localhost:5000"
LOGIN_URL = f"{BASE_URL}/login"
PATHOLOGY_QUERY_URL = f"{BASE_URL}/pathology_report/query"

def login_and_get_token():
    """登录并获取token"""
    login_data = {
        "username": "test_user",  # 请替换为实际的用户名
        "password": "test_password"  # 请替换为实际的密码
    }
    
    try:
        response = requests.post(LOGIN_URL, json=login_data)
        if response.status_code == 200:
            result = response.json()
            return result.get('token')
        else:
            print(f"登录失败: {response.status_code} - {response.text}")
            return None
    except Exception as e:
        print(f"登录请求失败: {e}")
        return None

def test_pathology_query(token):
    """测试病理报告查询接口"""
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    
    # 测试数据
    query_data = {
        "page": 1,
        "pageSize": 10
    }
    
    try:
        response = requests.post(PATHOLOGY_QUERY_URL, json=query_data, headers=headers)
        print(f"响应状态码: {response.status_code}")
        print(f"响应内容: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"查询成功!")
            print(f"数据条数: {len(result.get('data', []))}")
            print(f"分页信息: {result.get('pagination', {})}")
        else:
            print(f"查询失败: {response.text}")
            
    except Exception as e:
        print(f"请求失败: {e}")

def main():
    print("开始测试病理报告查询接口...")
    
    # 1. 登录获取token
    print("1. 正在登录...")
    token = login_and_get_token()
    
    if not token:
        print("无法获取token，测试终止")
        return
    
    print(f"登录成功，获取到token: {token[:20]}...")
    
    # 2. 测试查询接口
    print("\n2. 测试病理报告查询接口...")
    test_pathology_query(token)
    
    print("\n测试完成!")

if __name__ == "__main__":
    main()
