from app import db

class MedicalIndex(db.Model):
    index_id = db.Column(db.Integer, primary_key=True)
    index_name = db.Column(db.String(100), nullable=False)
    index_unit = db.Column(db.String(60))
    reference_min = db.Column(db.Float)
    reference_max = db.Column(db.Float)
    medical_type = db.Column(db.Integer, db.Foreign<PERSON>ey('medical_type.type_id'))
    is_chart = db.Column(db.String(1))
    description = db.Column(db.String(100))
    sort = db.Column(db.Integer)
    is_edit = db.Column(db.String(1))

class MedicalCheck(db.Model):
    medical_id = db.Column(db.Integer, primary_key=True)
    medical_date = db.Column(db.DateTime)
    user_id = db.Column(db.Integer, db.<PERSON>('user.user_id'))
    hospital = db.Column(db.String(20))
    medical_type = db.Column(db.Integer, db.<PERSON><PERSON>('medical_type.type_id'))
    comment = db.Column(db.String(255))
    status = db.Column(db.String(20))

class MedicalCheckDetail(db.Model):
    medical_detail_id = db.Column(db.Integer, primary_key=True)
    medical_id = db.Column(db.Integer, db.ForeignKey('medical_check.medical_id', ondelete='CASCADE'))
    index_name = db.Column(db.String(100), nullable=False)
    index_value = db.Column(db.String(60))
    index_unit = db.Column(db.String(60))
    reference_value = db.Column(db.String(100))
    index_status = db.Column(db.String(10))
    check = db.relationship('MedicalCheck', backref=db.backref('details', lazy=True, cascade='all, delete-orphan'))

class MedicalType(db.Model):
    type_id = db.Column(db.Integer, primary_key=True)
    type_name = db.Column(db.String(100), nullable=False)
    sort = db.Column(db.Integer)

class UserIndex(db.Model):
    user_id = db.Column(db.Integer, db.ForeignKey('user.user_id'), primary_key=True)
    index_id = db.Column(db.Integer, db.ForeignKey('medical_index.index_id'), primary_key=True)
    sort = db.Column(db.Integer)

class ExamType(db.Model):
    type_id = db.Column(db.Integer, primary_key=True)
    type_name = db.Column(db.String(100), nullable=False)
    sort = db.Column(db.Integer)

class MedicalExam(db.Model):
    exam_id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.user_id'))
    medical_date = db.Column(db.DateTime)
    hospital = db.Column(db.String(20))
    exam_type = db.Column(db.Integer, db.ForeignKey('exam_type.type_id'))
    exam_info = db.Column(db.String(500))
    exam_diag = db.Column(db.String(500))
    comment = db.Column(db.String(255))

class PathologyReport(db.Model):
    report_id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.user_id'))
    report_title = db.Column(db.String(40))
    report_date = db.Column(db.DateTime)
    hospital = db.Column(db.String(20))
    image = db.Column(db.blob)
    comment = db.Column(db.String(255))